import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/my_app.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:korrency/core/i18n/directory_asset_loader.dart';

// Listen to background messages
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  printty('Handling a background message ${message.messageId}');
  FirebasePushNotificationService.firebaseMessagingBackgroundHandler(message);
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  // Initialize environment configuration
  await EnvConfig.initialize("dev");
  await AppInitService().init(_firebaseMessagingBackgroundHandler);
  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('en'), Locale('fr')],
      path: 'assets/translations',
      assetLoader: const DirectoryAssetLoader(),
      fallbackLocale: const Locale('en'),
      useFallbackTranslations: true,
      saveLocale: true,
      child: const MyApp(),
    ),
  );
}
