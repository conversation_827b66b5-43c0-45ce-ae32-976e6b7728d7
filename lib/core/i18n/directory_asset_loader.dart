import 'dart:convert';
import 'dart:ui';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart' show rootBundle;

class DirectoryAssetLoader extends AssetLoader {
  const DirectoryAssetLoader();

  @override
  Future<Map<String, dynamic>> load(String path, Locale locale) async {
    final manifest = await rootBundle.loadString('AssetManifest.json');
    final assets = json.decode(manifest) as Map<String, dynamic>;
    final prefix = '$path/${locale.languageCode}/';
    final files = assets.keys.where((k) => k.startsWith(prefix) && k.endsWith('.json'));
    final Map<String, dynamic> data = {};
    for (final file in files) {
      final content = await rootBundle.loadString(file);
      final Map<String, dynamic> map = json.decode(content);
      data.addAll(map);
    }
    return data;
  }
}